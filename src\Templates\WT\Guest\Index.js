import React from 'react';
import Main from "./Main"
import { connect } from 'react-redux';
import * as HostActions from "../../../Actions/HostAction"
import JoiningPageHost from "./JoiningPageHost";
import * as ExceptionActions from "../../../Actions/Exception"
import {  Modalexception } from '../../../components/Exceptions';
import NewJoinRoom from './NewJoinRoom';
class Index extends React.Component{
  constructor(props){
    super(props);
this.Join=this.Join.bind(this);
if(!this.props.ClientData){
  var ClientData=localStorage.getItem(this.props.id);
  if(ClientData){
this.props.SetClientData(JSON.parse(ClientData))
console.log("SetClientData",ClientData)
  }
}

  }

componentDidMount()
{
  console.log(this.props.id)
  console.log("Index guest ")
  this.props.CheckInternetSpeed()

}
  Join() {
    console.log("Join() called with ClientData:", this.props.ClientData)

    // Safety check to ensure ClientData and name exist
    if (!this.props.ClientData || !this.props.ClientData.data || !this.props.ClientData.data.name) {
      console.error("Cannot join: ClientData or name is missing", this.props.ClientData);
      return;
    }

    this.props.ConnecToWebSocket(this.props.id, this.props.ClientData.data.name)
    }
  render(){
    if(this.props.SETUP){
      return(
        <>{this.props.ModalException?<Modalexception onclick={()=>this.props.SetModalException(false)}
        message={this.props.ModalException}/>
        :<></>}
        <Main  roomId={this.props.id} />
      </>)
    }
    else{
      return (<>
      {this.props.ModalException?<Modalexception onclick={()=>this.props.SetModalException(false)}
        message={this.props.ModalException}/>
        :<></>}
       <NewJoinRoom roomId={this.props.id}  Join={this.Join}/>
      </>);
    }
  }
}

const mapStateToProps = state => {
  return {
    SETUP:state.Call.SETUP,
    CanvasTrack:state.Call.CanvasTrack,
    ModalException:state.Exception.modalexception,
    DummyAudio:state.Call.DummyAudio,
    ClientData:state.Call.ClientData,
    ProjectDetails: state.Sessions.projectDetails,
    SessionDetails: state.Sessions.sessions,
  }
}

const mapDispatchToProps = {
  ...HostActions,
  ...ExceptionActions,
}

export default connect(mapStateToProps, mapDispatchToProps)(Index)
