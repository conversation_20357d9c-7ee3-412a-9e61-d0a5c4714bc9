import React from 'react'
import { connect } from "react-redux";
import * as HostActions from "../Actions/HostAction"
import { MicIcon, MicOffIcon, VideoIcon, VideoOffIcon } from '../assets/SvgIcons';
class VideoStream extends React.PureComponent {
  constructor(props) {
    super(props);
    this.videoRef = React.createRef()
    this.HandleVideo = this.HandleVideo.bind(this);
    this.HandleAudio = this.HandleAudio.bind(this);
  }
  componentDidMount() {
    const video = this.videoRef.current

    if ('srcObject' in video) {
      video.srcObject = this.props.LocalStream
    } else {
      video.src = window.URL.createObjectURL(this.props.LocalStream) // for older browsers
    }
  }
  HandleVideo() {

    if (this.props.Video) {
      this.props.LocalStream.removeTrack(this.props.LocalStream.getVideoTracks()[0])
      this.props.LocalStream.addTrack(HostActions.canvastrack)
      this.props.ToogleLocalAV("Video", false)
    }
    else {
      navigator.mediaDevices.getUserMedia({ video: true }).then(stream => {
        this.props.LocalStream.removeTrack(this.props.LocalStream.getVideoTracks()[0])
        this.props.LocalStream.addTrack(stream.getVideoTracks()[0]);
        this.props.ToogleLocalAV("Video", true)
      })
    }
  }
  HandleAudio() {
    if (this.props.Audio) {
      this.props.LocalStream.getAudioTracks()[0].enabled = false;
      this.props.ToogleLocalAV("Audio", false)
    }
    else {

      this.props.LocalStream.getAudioTracks()[0].enabled = true;
      this.props.ToogleLocalAV("Audio", true)

    }
  }
  Strength(position, network) {
    switch (network.strength) {
      case "WEEK":
        if (position <= 1) {
          return "red"
        } else {
          return "#ff00006e"
        }
      case "MODERATE":
        if (position <= 2) {
          return "#f57a06"
        } else {
          return "#f57a068f"
        }
      case "BETTER":
        if (position <= 3) {
          return "#3fff08"
        } else {
          return "#3fff08"
        }
      case "EXCELLENT":
        if (position <= 3) {
          return "#3fff08"
        } else {
          return "#3fff08"
        }
      default:
        return "#fff"
    }
  }
  componentDidUpdate() {


  }

  render() {

    return (<>

      <div className='h-fit w-full'>
        <div className='w-full h-auto pt-[56.25%] relative'>
          <video className="rounded-[10px] left-0 top-0 absolute w-full h-full object-none"
            autoPlay muted={true}
            ref={this.videoRef}
          />
          <div
            className='flex flex-row absolute bottom-1 lg:bottom-3 left-1/2 -translate-x-2/4 bg-[transparent] rounded p-2'>
            <div className="mr-3 flex justify-end " >
              <button className={`w-10 h-10 rounded-full ${this.props.Video ? 'bg-white' : 'bg-red-100 text-white'} flex items-center justify-center shadow-lg`} onClick={this.HandleAudio}>
                {this.props.Audio ?
                  <MicIcon />
                  :
                  <MicOffIcon />
                }
              </button>
            </div>
            <div >
              <button onClick={this.HandleVideo} className={`w-10 h-10 rounded-full ${this.props.Audio ? 'bg-white' : 'bg-red-100 text-white'} flex items-center justify-center shadow-lg`}>

                {this.props.Video ?

                  <VideoIcon /> :
                  <VideoOffIcon />
                }


              </button>
            </div>
          </div>
        </div>

      </div>
    </>
    )
  }
}
const mapStateToProps = state => {
  return {
    LocalStream: state.Call.LocalStream,
    Video: state.Call.Video,
    Audio: state.Call.Audio,
    NetWorkSpeed: state.Call.NetWorkSpeed,
    DummyAudio: state.Call.DummyAudio
  }
}

const mapDispatchToProps = {
  ...HostActions
}

export default connect(mapStateToProps, mapDispatchToProps)(VideoStream)